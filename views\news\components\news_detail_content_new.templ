package components

templ NewsDetailContent() {
<main class="container container-detail">
	@newsDetailHeader()
	@newsDetailNote()
	@newsDetailSummary()
	@newsDetailMainContent()
	@newsDetailFooter()
	@newsDetailRelatedPosts()
	// @newsDetailScrollTop()
</main>
}

// Header section với title và meta
templ newsDetailHeader() {
<header class="detail-header">
	<h1 class="title-detail">XU HƯỚNG THỜI TRANG MÙA XUÂN 2025: MINIMALISM VÀ SUSTAINABLE FASHION</h1>
	<ul class="article-meta mb-1">
		<li class="article-meta-item">
			<span class="article-meta-icon">
				<svg width="18" height="18" viewBox="0 0 16 16" xmlns:xlink="http://www.w3.org/1999/xlink">
					<use xlink:href="#eye-icon" fill="currentColor"></use>
				</svg>
			</span>
			<span>2.847</span>
		</li>
		<li class="article-meta-item">
			<span class="article-meta-icon">
				<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
					<use xlink:href="#calendar-icon"></use>
				</svg>
			</span>
			<span>31/01/2025</span>
		</li>
	</ul>
</header>
}

// Note section
templ newsDetailNote() {
<div class="detail-note">
	Bài viết được biên soạn bởi đội ngũ Fashion Editor của Maybi,
	<br />
	với sự tham vấn từ các chuyên gia thời trang quốc tế và nhà thiết kế Việt Nam.
</div>
}

// Summary section
templ newsDetailSummary() {
<div class="detail-des">
	Khám phá những xu hướng thời trang mùa xuân 2025 với phong cách minimalism và sustainable fashion - hai trào lưu
	đang định hình lại ngành thời trang toàn cầu.
</div>
}

// Main content section
templ newsDetailMainContent() {
<div class="content-detail my-2">
	@contentSection1()
	@contentSection2()
	@contentImage()
	@contentSection3()
	@contentReferences()
</div>
}

// Content section 1: Minimalism trong thời trang
templ contentSection1() {
<h2><strong>Minimalism - Triết lý "Less is More" trong thời trang</strong></h2>
<p>Minimalism không chỉ đơn thuần là một xu hướng thời trang mà còn là một triết lý sống. Trong thế giới thời trang,
	minimalism thể hiện qua những thiết kế tinh giản, màu sắc trung tính và form dáng cơ bản nhưng tinh tế. Phong cách
	này giúp người mặc tạo nên vẻ đẹp thanh lịch, sang trọng mà không cần quá nhiều chi tiết rườm rà.</p>
<p>Xu hướng minimalist đang được các fashionista trên toàn thế giới ưa chuộng bởi tính ứng dụng cao và khả năng
	mix-match linh hoạt. Một chiếc áo sơ mi trắng basic có thể kết hợp với quần âu để đi làm, hoặc với jeans để dạo phố,
	tạo nên những outfit hoàn toàn khác biệt.</p>
}

// Content section 2: Sustainable Fashion
templ contentSection2() {
<h2><strong>Sustainable Fashion - Thời trang bền vững cho tương lai</strong></h2>
<p>Sustainable fashion hay thời trang bền vững đang trở thành xu hướng không thể thiếu trong ngành công nghiệp thời
	trang hiện đại. Đây không chỉ là trào lưu nhất thời mà là sự thay đổi căn bản trong cách chúng ta sản xuất, tiêu
	dùng và nhìn nhận về thời trang.</p>
<p>Các thương hiệu thời trang bền vững tập trung vào việc sử dụng chất liệu tái chế, organic cotton, hemp và các vật
	liệu thân thiện với môi trường. Điều này không chỉ giúp giảm thiểu tác động tiêu cực đến môi trường mà còn tạo ra
	những sản phẩm chất lượng cao, bền đẹp theo thời gian.</p>
<p>Theo báo cáo của Global Fashion Agenda, ngành thời trang là một trong những ngành gây ô nhiễm môi trường nhiều thứ
	hai thế giới. Việc chuyển đổi sang thời trang bền vững không chỉ là trách nhiệm của các thương hiệu mà còn của người
	tiêu dùng trong việc lựa chọn những sản phẩm có ý thức về môi trường.</p>
}

// Content image
templ contentImage() {
<p style="text-align: center;">
	<img alt="Xu hướng thời trang minimalism và sustainable fashion 2025"
		src="/static/images/blogs/blog-minimalist.jpg" />
</p>
}

// Content section 3: Kết luận và xu hướng tương lai
templ contentSection3() {
<p>Minimalism và sustainable fashion không chỉ là xu hướng thời trang mà còn là lối sống bền vững cho tương lai. Việc
	đầu tư vào những món đồ chất lượng, thiết kế tinh giản và thân thiện với môi trường sẽ giúp bạn xây dựng tủ đồ hoàn
	hảo, vừa tiết kiệm chi phí vừa góp phần bảo vệ hành tinh xanh.</p>
<p>*Bài viết được biên soạn dựa trên nghiên cứu từ Global Fashion Agenda, Fashion Revolution và các chuyên gia thời
	trang quốc tế. Maybi cam kết mang đến những thông tin chính xác và hữu ích nhất cho độc giả.</p>
}

// References section
templ contentReferences() {
<p><strong>Tài liệu tham khảo:</strong></p>
<p><i>1/Global Fashion Agenda. "Fashion on Climate: How the fashion industry can urgently act to reduce its climate impact" (2020)</i></p>
<p><i>2/Fashion Revolution. "Fashion Transparency Index 2023" - https://www.fashionrevolution.org/about/transparency/(Truy cập 31/01/2025)</i></p>
<p><i>3/Ellen MacArthur Foundation. "A new textiles economy: Redesigning fashion's future" (2017)</i></p>
<p><i>4/McKinsey & Company. "The State of Fashion 2025: Navigating uncertainty" (2024)</i></p>
<p><i>5/Vogue Business. "Sustainable Fashion Trends 2025" - https://www.voguebusiness.com/sustainability (Truy cập 30/01/2025)</i></p>
<p><i>6/Common Objective. "The Issues: Minimalism in Fashion" - https://www.commonobjective.co (Truy cập 29/01/2025)</i></p>

}

// Footer section với social sharing
templ newsDetailFooter() {
<section class="mt-2">
	<ul class="article-meta">
		<li class="article-meta-item">
			<span class="article-meta-icon">
				<svg width="18" height="18" viewBox="0 0 16 16" xmlns:xlink="http://www.w3.org/1999/xlink">
					<use xlink:href="#eye-icon" fill="currentColor"></use>
				</svg>
			</span>
			<span>2.847</span>
		</li>
		<li class="article-meta-item">
			<a class="article-meta-icon" href="javascript: void(0);" id="fb-share-icon">
				<svg width="18" height="18" viewBox="0 0 16 16" xmlns:xlink="http://www.w3.org/1999/xlink">
					<use xlink:href="#icon-facebook"></use>
				</svg>
			</a>
		</li>
		<li class="article-meta-item">
			<a class="article-meta-icon" href="javascript:void(0);" id="mail-share-btn">
				<svg width="18" height="18" viewBox="0 0 16 16" xmlns:xlink="http://www.w3.org/1999/xlink">
					<use xlink:href="#icon-mail"></use>
				</svg>
			</a>
		</li>
		<li class="article-meta-item">
			<a class="article-meta-icon" href="javascript: void(0);" id="copy-link">
				<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<use xlink:href="#link-icon" fill="currentColor"></use>
				</svg>
			</a>
		</li>
	</ul>
	<div id="copyToast">Copy link thành công</div>
</section>
}

// Related posts section
templ newsDetailRelatedPosts() {
<section class="posts-related">
	<div class="posts-related-list posts-related-list--grid4">
		@relatedPostItem("XU HƯỚNG THỜI TRANG MÙA HÈ 2025: MÀNG SẮC TƯƠI SÁNG VÀ CHẤT LIỆU THOÁNG MÁT","/blogs/xu-huong-thoi-trang-mua-he-2025", "/static/images/blogs/blog-trend-spring.png", "Khám phá những xu hướng thời trang hot nhất mùa hè 2025 với màu sắc rực rỡ và chất liệu thân thiện môi trường", "156", "3.241")
		@relatedPostItem("PHONG CÁCH MINIMALIST: BÍ QUYẾT TẠO TỦ ĐỒ HOÀN HẢO","/blogs/phong-cach-minimalist-tu-do-hoan-hao", "/static/images/blogs/blog-minimalist.jpg", "Hướng dẫn chi tiết cách xây dựng tủ đồ minimalist với những món đồ cơ bản nhưng đa năng và tinh tế", "89", "1.892")
		@relatedPostItem("SUSTAINABLE FASHION: THỜI TRANG BỀN VỮNG CHO TƯƠNG LAI","/blogs/sustainable-fashion-tuong-lai", "/static/images/blogs/blog-collection-new.jpg", "Tìm hiểu về thời trang bền vững và cách lựa chọn những thương hiệu có trách nhiệm với môi trường", "203", "2.567")
		@relatedPostItem("MIX & MATCH: CÁCH PHỐI ĐỒ THÔNG MINH CHO PHÁI ĐẸP", "/blogs/mix-match-phoi-do-thong-minh","/static/images/blogs/blog-office-style.jpg", "Bí quyết phối đồ thông minh giúp bạn tạo ra nhiều outfit khác nhau từ những món đồ cơ bản trong tủ", "124", "1.456")
	</div>
</section>
}

// Individual related post item
templ relatedPostItem(title, url, image, description, likes, views string) {
<article class="posts-related-item">
	<figure class="posts-related-item-img">
		<a href={ templ.SafeURL(url) } title={ title }>
			<img src={ image } alt={ title } />
		</a>
	</figure>
	<header class="posts-related-item-header">
		<h3 class="posts-related-item-title">
			<a class="line-clamp-3" href={ templ.SafeURL(url) } title={ title }>{ title }</a>
		</h3>
		<p class="posts-related-item-des">{ description }</p>
		<ul class="posts-related-item-meta">
			<li class="posts-related-item-meta-item">
				<span class="posts-related-item-meta-icon">
					<svg width="12" height="11" viewBox="0 0 12 11" fill="none">
						<use xlink:href="#liked-icon" fill="currentColor"></use>
					</svg>
				</span>
				<span>{ likes }</span>
			</li>
			<li class="posts-related-item-meta-item">
				<span class="posts-related-item-meta-icon">
					<svg xmlns:xlink="http://www.w3.org/1999/xlink">
						<use xlink:href="#eye-icon" fill="currentColor"></use>
					</svg>
				</span>
				<span>{ views }</span>
			</li>
		</ul>
	</header>
</article>
}