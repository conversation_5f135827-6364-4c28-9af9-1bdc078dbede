package components

templ BlogsBoSuuTapCpn() {
<div class="library-grid">
    @blogBoSuuTapLeftItem("Bộ sưu tập Thu Đông 2025: Elegance & Warmth","/bo-suu-tap/thu-dong-2025-elegance-warmth.html", "/static/images/blogs/blog-collection-new.jpg", "Khám phá bộ sưu tập thu đông mới với sự kết hợp hoàn hảo giữa sự thanh lịch và ấm áp cho mùa lạnh.", "4.200", "1.580")

    <!-- 4 bài viết nhỏ bên phải -->
    <div class="library-articles">
        @blogBoSuuTapRightItem("Spring Collection 2025: Fresh & Vibrant","/bo-suu-tap/spring-collection-2025-fresh.html", "/static/images/blogs/blog-trend-spring.png", "3.850","1.320")
        @blogBoSuuTapRightItem("Capsule Wardrobe: Minimalist Collection","/bo-suu-tap/capsule-wardrobe-minimalist.html", "/static/images/blogs/blog-minimalist.jpg", "3.150","1.080")
        @blogBoSuuTapRightItem("Evening Glamour: Luxury Collection", "/bo-suu-tap/evening-glamour-luxury.html","/static/images/blogs/blog-minimalist.jpg", "4.650", "1.890")
        @blogBoSuuTapRightItem("Sustainable Fashion: Eco Collection", "/bo-suu-tap/sustainable-fashion-eco.html","/static/images/blogs/blog-collection-new-2.jpg", "2.950", "890")
    </div>
</div>
}


templ blogBoSuuTapLeftItem(title, pagePath, img, description, views, likes string) {
<article class="library-featured">
    <div class="library-featured-cont-img">
        <img src={img} alt={title} class="library-featured-img" />
    </div>
    <div class="library-featured-content">
        <h3 class="library-featured-title txt-hover-green">
            {title}
        </h3>
        <div class="library-featured-desc">
            {description}
        </div>
        <div class="gender-article-meta">
            <div class="nitem__support-item">
                <div class="nitem__support-icon">
                    <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                        <use xlink:href="#icon-like-blog"></use>
                    </svg>
                </div>
                <span>{likes}</span>
            </div>
            <div class="nitem__support-item">
                <div class="nitem__support-icon">
                    <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                        <use xlink:href="#icon-eye-blog"></use>
                    </svg>
                </div>
                <span>{views}</span>
            </div>
        </div>
    </div>
</article>
}

templ blogBoSuuTapRightItem(title, pagePath, img, views, likes string) {
<article class="nitem lib--col liba__grid--col">
    <div class="lib__thumb lib__thumb--col">
        <a href={templ.SafeURL(pagePath)} title={title} class="nitem__thumb-link">
            <img class="lib__thumb-img ana__thumb-img--row" src={img} alt={title}>
        </a>
    </div>
    <div class="lib__content lib__content--col">
        <h3 class="lib__title">
            <a class="txt-hover-green" href={templ.SafeURL(pagePath)} title={title}>{title}</a>
        </h3>
        <div class="lib__bottom">
            <ul class="lib__support">
                <li class="lib__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-like-blog"></use>
                        </svg>
                    </div>
                    <span>{likes}</span>
                </li>
                <li class="lib__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-eye-blog"></use>
                        </svg>
                    </div>
                    <span>{views}</span>
                </li>
            </ul>
        </div>
    </div>
</article>
}