const appMain = (function () {
  const htmlEl = document.querySelector("html");
  const searchDropdown = document.querySelector(".search__dropdown");
  const btnSearch = document.querySelector(".search-link");

  const dropdownTitle = document.getElementById("search-cate");
  const dropdownList = document.querySelector(".search-category__dropdown");
  let colors = document.querySelectorAll(".product-color");
  const maxColorVisible = 6;

  const headerSticky = () => {
    const body = document.body;
    const headerContainer = document.querySelector(".header-wrapper");
    const headerTop = document.querySelector(".header-info-bar");
    const topHeaderHeight = headerTop.offsetHeight || 0;
    const targetScroll = topHeaderHeight + 200;

    window.addEventListener("scroll", function () {
      if (!body.classList.contains("maybi-header-sticky")) return; // Early exit for efficiency
      if (window.scrollY > targetScroll) {
        headerContainer.classList.add("maybi-sticky");
      } else {
        headerContainer.classList.remove("maybi-sticky");
      }
    });
  };

  const searchCategoryDropdown = () => {
    if (!dropdownTitle || !dropdownList) return;

    // Toggle dropdown when title clicked
    dropdownTitle.addEventListener("click", () => {
      dropdownList.classList.toggle("show");
    });

    // Handle click on each dropdown item
    const items = dropdownList.querySelectorAll("li[data-value]");
    items.forEach((item) => {
      item.addEventListener("click", () => {
        const selectedText = item.textContent.trim();
        const selectedValue = item.getAttribute("data-value");

        // Update dropdown title
        dropdownTitle.textContent = selectedText;
        dropdownTitle.setAttribute("data-value", selectedValue);

        // Update active state
        items.forEach((i) => i.classList.remove("active"));
        item.classList.add("active");
        // Close dropdown
        dropdownList.classList.remove("show");
      });
    });

    // Close dropdown if clicked outside
    document.addEventListener("click", (e) => {
      if (
        !dropdownTitle.contains(e.target) &&
        !dropdownList.contains(e.target)
      ) {
        dropdownList.classList.remove("show");
      }
    });
  };

  const btnSearchClick = () => {
    if (!btnSearch) return;
    btnSearch.addEventListener("click", function (e) {
      e.preventDefault();
      htmlEl.classList.toggle("search-opened");
      this.classList.toggle("open");
      searchDropdown.classList.toggle("active");
    });
  };

  const closeSearchDropdown = () => {
    document.addEventListener("click", function (e) {
      if (htmlEl.classList.contains("search-opened")) {
        closeSideNav(e);
      }
    });
  };

  const closeSideNav = function (e) {
    if (!searchDropdown.contains(e.target) && !btnSearch.contains(e.target)) {
      htmlEl.classList.toggle("search-opened");
      btnSearch.classList.toggle("open");
      searchDropdown.classList.toggle("active");
    }
  };

  const showColor = () => {
    colors.forEach((color) => {
      color.addEventListener("click", (e) => {
        let productId = color.getAttribute("data-id");
        let pathImg = color.getAttribute("data-img");
        if (!productId || !pathImg) return;

        const parentProduct = color.closest(".product");
        const siblingColors = parentProduct.querySelectorAll(".product-color");
        siblingColors.forEach((i) => i.classList.remove("active"));

        color.classList.add("active");
        parentProduct
          .querySelector("#" + productId)
          .setAttribute("src", pathImg);
      });
    });
  };

  const changePruductColor = () => {
    document.querySelectorAll(".product").forEach((product) => {
      product.addEventListener("click", (e) => {
        const color = e.target.closest(".product-color");
        if (!color || color.classList.contains("product-color--more")) return;
        const img = color.getAttribute("data-img");
        const product = color.closest(".product");
        const mainImg = product.querySelector(".product__img");
        const allColors = [...product.querySelectorAll(".product-color")]
                .filter(el => el.closest(".product") === product);
        allColors.forEach((c) => c.classList.remove("active"));
        color.classList.add("active");
        if (mainImg && img) {
          mainImg.setAttribute("src", img);
        }
      });
    });
  };

  const changePruductSize = () => {
    const sizes = document.querySelectorAll(".product-size__item");
    sizes.forEach((item) => {
      item.addEventListener("click", (el) => {
        if (!el.target.dataset.color) return;
        const colorItems = JSON.parse(el.target.dataset.color);
        const productWrapper = el.currentTarget.closest(".product");
        const productInfo = productWrapper.querySelector(".product__info");
        const productColors = productInfo.querySelector(".product-colors");

        const currentActiveColor = productColors.querySelector(
          ".product-color.active"
        );
        const currentImg = currentActiveColor?.getAttribute("data-img");
        let colorHTML = "";
        const visibleColors = colorItems.slice(0, maxColorVisible);
        const remaining = colorItems.length - maxColorVisible;
        visibleColors.forEach((colorItem) => {
          colorHTML += `
              <li class="product-color" data-img="${colorItem.img}" data-id="${colorItem.id || ''}">
                <span class="product-color__item" style="background-color: ${colorItem.color};"></span>
              </li>`;
        });

        if (remaining > 0) {
          colorHTML += `
              <li class="product-color product-color--more">
                <span>+${remaining}</span>
              </li>`;
        }

        productColors.innerHTML = colorHTML;

        const newColorElements =
          productColors.querySelectorAll(".product-color");
        let foundMatch = false;

        newColorElements.forEach((colorEl) => {
          const product_img = getNormalizedPath(colorEl.getAttribute("data-img"));
          const product_img_hover = getNormalizedPath(currentImg);
          if (product_img === product_img_hover) {
            colorEl.classList.add("active");
            foundMatch = true;
          }
        });

        if (!foundMatch && newColorElements.length > 0) {
          newColorElements[0].classList.add("active");
        }

        const activeColor = productColors.querySelector(
          ".product-color.active"
        );
        const newImg = activeColor?.getAttribute("data-img");
        const mainImg = productWrapper.querySelector(".product__img");
        if (mainImg && newImg) {
          mainImg.setAttribute("src", newImg);
        }

        const activeSize = productWrapper.querySelector(
          ".product-size--active"
        );
        if (activeSize) {
          activeSize.classList.remove("product-size--active");
        }
        el.currentTarget.classList.add("product-size--active");

        changePruductColor();
      });
    });
  };

  const btnMenuMobileBar = () => {
    const menuMobileBar = document.querySelector(".menu-mobile-bar");
    const menusMobile = document.querySelector(".menus-mobile");
    const exitBtn = document.querySelector(".m-exit-btn");

    if (!menuMobileBar) return;

    // Function to close mobile menu
    const closeMobileMenu = () => {
      menuMobileBar.classList.remove("open");
      menusMobile.classList.remove("show");
      document.body.classList.remove("no-scroll");
    };

    // Function to open mobile menu
    const openMobileMenu = () => {
      menuMobileBar.classList.add("open");
      menusMobile.classList.add("show");
      document.body.classList.add("no-scroll");
    };

    // Toggle menu when hamburger button is clicked
    menuMobileBar.addEventListener("click", function () {
      if (menusMobile.classList.contains("show")) {
        closeMobileMenu();
      } else {
        openMobileMenu();
      }
    });

    // Close menu when exit button is clicked
    if (exitBtn) {
      exitBtn.addEventListener("click", closeMobileMenu);
    }
  };

  const bindQuantityControl = () => {
    document
      .querySelectorAll(".quantity-control")
      .forEach(function (control) {
        const input = control.querySelector(".quantity-input");
        const btnUp = control.querySelector(".quantity__btn-up");
        const btnDown = control.querySelector(".quantity__btn-down");

        btnUp.addEventListener("click", function () {
          let value = parseInt(input.value, 10) || 0;
          input.value = value + 1;
        });

        btnDown.addEventListener("click", function () {
          let value = parseInt(input.value, 10) || 0;
          if (value > 1) {
            input.value = value - 1;
          }
        });
      });
  };
  const getNormalizedPath = (url) => {
    try {
      return new URL(url, location.origin).pathname;
    } catch (e) {
      return "";
    }
  };

  const openOffcanvasCart = () => {
    const cartSidebar = document.getElementById("cartSidebar");
    document.addEventListener("click", function (e) {
      const btn = e.target.closest(".open-cart-offcanvas");
      if (!btn) return;
      cartSidebar?.classList.add("is-open");
    });
  };

  const closeOffcanvasCart= () => {
    const cartSidebar = document.getElementById("cartSidebar");
    const cartPanel = cartSidebar.querySelector(".sheet__panel");
    const closeBtn = cartPanel.querySelector(".sheet__close");

    closeBtn.addEventListener("click", () => {
      cartSidebar.classList.remove("is-open");
    });

    cartSidebar.addEventListener("click", (e) => {
      if (!cartPanel.contains(e.target)) {
        cartSidebar.classList.remove("is-open");
      }
    });
  };

  const hasShow = () => {
    const hasShowBtn = document.querySelectorAll(".has-show");
    if (!hasShowBtn.length) return;
    hasShowBtn.forEach(function (el) {
      el.addEventListener("click", function (e) {
        const isLink = e.target.closest(".has-item");
        if (isLink) return;
        hasShowBtn.forEach(function (item) {
          if (item !== el) {
            item.classList.remove("show");
          }
        });

        e.preventDefault();
        this.classList.toggle("show");
      });
    });
  };

  const footerExtraNav  = () => {
    const nav = document.querySelector(".m_extra_nav");
    const bottomThreshold = 100;
    let lastScrollTop = 0;
    window.addEventListener("scroll", function () {
      const scrollTop = window.scrollY || window.pageYOffset;
      const windowHeight = window.innerHeight;
      const docHeight = document.documentElement.scrollHeight;
      const isScrollingDown = scrollTop > lastScrollTop;
      lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
      if (scrollTop + windowHeight >= docHeight - bottomThreshold) {
        nav.classList.add("active");
        return;
      }
      if (isScrollingDown) {
        nav.classList.add("active");
      } else {
        nav.classList.remove("active");
      }
    });
  };

  const initChooseRatingStars = () => {
    const ratingBlocks = document.querySelectorAll('.star-rating');
    ratingBlocks.forEach((block) => {
      const stars = block.querySelectorAll('.mb-star');
      const input = block.querySelector('.star-rating-value');
      const initialRating = parseInt(block.getAttribute('data-rating')) || 0;

      const updateStars = (rating) => {
        stars.forEach((s, i) => {
          const use = s.querySelector('use');
          if (use) {
            use.setAttribute('href', i < rating ? '#icon-star-active' : '#icon-star-fill');
          }
        });

        if (input) input.value = rating;
        block.setAttribute('data-rating', rating);
      };

      if (initialRating > 0) {
        updateStars(initialRating);
      }

      stars.forEach((star, index) => {
        star.addEventListener('click', () => {
          updateStars(index + 1);
        });
      });
    });
  };


  return {
    init: function () {
      headerSticky();
      searchCategoryDropdown();
      btnSearchClick();
      closeSearchDropdown();
      showColor();
      changePruductColor();
      changePruductSize();
      btnMenuMobileBar();
      bindQuantityControl();
      openOffcanvasCart();
      closeOffcanvasCart();
      hasShow();
      footerExtraNav();
      initChooseRatingStars();
    },
  };
})();

document.addEventListener("DOMContentLoaded", function (event) {
  appMain.init();
});