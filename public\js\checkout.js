const offcanvas = document.getElementById("offcanvas_voucher");
const radios = document.querySelectorAll('input[name="selectedVoucher"]');
const checkoutVouchersShow = document.querySelector(".cart-vouchers__show");

const checkout = (function () {
  const openOffcanvasVoucherPanel = () => {
    document.addEventListener("click", function (e) {
      const btn = e.target.closest(".btn-open-vouchers");
      if (!btn) return;
      offcanvas?.classList.add("is-open");
    });
  };

  const closeOffcanvasVouchers = () => {
    const checkoutPanel = document.querySelector(".sheet__panel");
    const closeBtn = document.querySelector(".sheet__close");

    if (!offcanvas || !checkoutPanel || !closeBtn) return;

    closeBtn.addEventListener("click", () => {
      offcanvas.classList.remove("is-open");
    });

    offcanvas.addEventListener("click", (e) => {
      if (!checkoutPanel.contains(e.target)) {
        offcanvas.classList.remove("is-open");
      }
    });
  };

  const renderSelectedVoucher = () => {

  };

  const handleDiscountCodeInput = () => {
    const input = document.getElementById("discount_code");
    const button = document.querySelector(".summary-discount__btn-submit");

    if (!input || !button) return;

    input.addEventListener("input", () => {
      if (input.value.trim() !== "") {
        button.removeAttribute("disabled");
      } else {
        button.setAttribute("disabled", "true");
      }
    });
  };

  function initToggleOrderForOthers() {
    const toggleCheckbox = document.getElementById("toggle_order_for_others");
    const orderForOthersContent = document.querySelector(".order-for-others__content");

    if (!toggleCheckbox || !orderForOthersContent) return;
    const updateVisibility = () => {
      const isChecked = toggleCheckbox.checked;
      if (isChecked) {
        orderForOthersContent.style.display = "block";
        orderForOthersContent.removeAttribute("hidden");
        orderForOthersContent.setAttribute("aria-hidden", "false");
      } else {
        orderForOthersContent.style.display = "none";
        orderForOthersContent.setAttribute("hidden", "");
        orderForOthersContent.setAttribute("aria-hidden", "true");
      }
    };

    toggleCheckbox.addEventListener("change", updateVisibility);
    updateVisibility();
  }



  return {
    init: function () {
      closeOffcanvasVouchers();
      openOffcanvasVoucherPanel();
      renderSelectedVoucher();
      handleDiscountCodeInput();
      initToggleOrderForOthers();
    },
  };
})();

// Gift Modal Functionality
const giftModal = (function () {
  const modal = document.getElementById('giftSizeModal');
  const openBtn = document.getElementById('openGiftSizeModal');
  const closeBtn = document.getElementById('closeGiftSizeModal');
  const cancelBtn = document.getElementById('cancelGiftSize');
  const confirmBtn = document.getElementById('confirmGiftSize');
  const sizeItems = document.querySelectorAll('.gift-size-list .product-size__item');

  let selectedSize = null;
  let giftData = null;

  const openModal = () => {
    if (!modal) return;

    // Get gift data from the gift content element
    const giftContent = document.querySelector('.gift-product__content');
    if (giftContent) {
      giftData = {
        id: giftContent.dataset.giftId,
        name: giftContent.dataset.giftName,
        image: giftContent.dataset.giftImage,
        price: giftContent.dataset.giftPrice,
        originalPrice: giftContent.dataset.giftOriginalPrice,
        currency: giftContent.dataset.giftCurrency,
        sizes: JSON.parse(giftContent.dataset.giftSizes || '[]'),
        colors: JSON.parse(giftContent.dataset.giftColors || '[]'),
        sku: giftContent.dataset.giftSku,
        condition: giftContent.dataset.giftCondition
      };

      // Update modal content
      updateModalContent();
    }

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    if (!modal) return;

    modal.classList.remove('active');
    document.body.style.overflow = '';
    selectedSize = null;

    // Reset size selection
    sizeItems.forEach(item => {
      item.classList.remove('product-size--active');
    });
  };

  const updateModalContent = () => {
    if (!giftData) return;

    // Update modal image and info
    const modalImage = document.getElementById('giftModalImage');
    const modalTitle = document.getElementById('giftModalTitle');
    const modalDescription = document.getElementById('giftModalDescription');

    if (modalImage) modalImage.src = giftData.image;
    if (modalTitle) modalTitle.textContent = giftData.name;
    if (modalDescription) modalDescription.textContent = giftData.condition;
  };

  const handleSizeSelection = () => {
    sizeItems.forEach(item => {
      item.addEventListener('click', function () {
        // Remove active class from all items
        sizeItems.forEach(sizeItem => {
          sizeItem.classList.remove('product-size--active');
        });

        // Add active class to clicked item
        this.classList.add('product-size--active');
        selectedSize = this.dataset.size;

        // Enable confirm button
        if (confirmBtn) {
          confirmBtn.disabled = false;
        }
      });
    });
  };

  const handleConfirm = () => {
    if (!selectedSize || !giftData) {
      alert('Vui lòng chọn size');
      return;
    }

    // Create gift item object
    const giftItem = {
      id: giftData.id,
      name: giftData.name,
      image: giftData.image,
      size: selectedSize,
      price: giftData.price,
      originalPrice: giftData.originalPrice,
      currency: giftData.currency,
      sku: giftData.sku,
      quantity: 1,
      isGift: true
    };

    // Add to cart or handle gift selection
    addGiftToOrder(giftItem);

    // Close modal
    closeModal();
  };

  const addGiftToOrder = (giftItem) => {
    // Convert gift item to cart-compatible format
    const cartGiftItem = {
      product_id: giftItem.id,
      name: giftItem.name,
      image: giftItem.image,
      price: giftItem.price + giftItem.currency,
      originalPrice: giftItem.originalPrice + giftItem.currency,
      quantity: giftItem.quantity,
      selectedSize: giftItem.size,
      selectedColor: '#000000', // Default color for gift
      sizes: [
        { text: giftItem.size, isActive: true, dataColor: '#000000' }
      ],
      colors: [
        { backgroundColor: '#000000', dataImg: giftItem.image, isActive: true }
      ],
      isGift: true
    };

    // Use cartUpdater to add gift to sidebar cart
    if (typeof cartUpdater !== 'undefined' && cartUpdater.addGiftProductToCart) {
      cartUpdater.addGiftProductToCart(cartGiftItem);
    } else {
      console.error('cartUpdater not available');
    }

    // Try adding to localStorage instead of direct DOM manipulation
    addGiftToLocalStorage(giftItem);

    // Also try direct DOM manipulation as fallback
    setTimeout(() => {
      addGiftToOrderDetailList(giftItem);
    }, 100);

    // Update the gift button to show "Đã chọn"
    if (openBtn) {
      openBtn.innerHTML = '<span>Đã chọn (Size: ' + giftItem.size + ')</span>';
      openBtn.classList.add('btn--success');
      openBtn.disabled = true;
    }
  };

  const addGiftToLocalStorage = (giftItem) => {
    try {
      // Get existing cart items from localStorage
      let cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');

      // Remove existing gift items
      cartItems = cartItems.filter(item => !item.isGift);

      // Create gift item for localStorage
      const giftCartItem = {
        product_id: giftItem.id,
        name: giftItem.name,
        image: giftItem.image,
        price: giftItem.price + giftItem.currency,
        originalPrice: giftItem.originalPrice + giftItem.currency,
        quantity: giftItem.quantity,
        selectedSize: giftItem.size,
        selectedColor: '#000000',
        sizes: [
          { text: giftItem.size, isActive: true, dataColor: '#000000' }
        ],
        colors: [
          { backgroundColor: '#000000', dataImg: giftItem.image, isActive: true }
        ],
        isGift: true
      };

      // Add gift item to cart
      cartItems.push(giftCartItem);

      // Save back to localStorage
      localStorage.setItem('cartItems', JSON.stringify(cartItems));

      console.log('Gift item added to localStorage:', giftCartItem);

      // Trigger cart re-render if available
      if (typeof cartUpdater !== 'undefined' && cartUpdater.renderCartItems) {
        const checkoutRight = document.querySelector('.checkout__right');
        if (checkoutRight) {
          cartUpdater.renderCartItems(checkoutRight);
        }
      }

    } catch (error) {
      console.error('Error adding gift to localStorage:', error);
    }
  };

  const addGiftToOrderDetailList = (giftItem) => {
    // Find ALL order detail lists (desktop + mobile)
    const orderDetailLists = document.querySelectorAll('.order-detail__list');

    if (orderDetailLists.length === 0) {
      console.error('No order detail lists found');
      return;
    }

    console.log(`Found ${orderDetailLists.length} order detail lists:`, orderDetailLists);

    // Add gift item to each order detail list
    orderDetailLists.forEach((orderDetailList, index) => {
      const listType = index === 0 ? 'desktop' : 'mobile';
      console.log(`Adding gift to ${listType} order detail list:`, orderDetailList);

      // Check if gift item already exists
      const existingGiftItem = orderDetailList.querySelector('.order-detail__item.gift-item');
      if (existingGiftItem) {
        console.log('Removing existing gift item');
        existingGiftItem.remove();
      }

      // Try cloning an existing item first
      const existingItem = orderDetailList.querySelector('.order-detail__item');
      let giftItemLi;

      if (existingItem) {
        // Clone existing item and modify it
        giftItemLi = existingItem.cloneNode(true);
        giftItemLi.className = 'order-detail__item gift-item';

        // Update the content
        const badge = document.createElement('div');
        badge.className = 'gift-badge';
        badge.textContent = 'Tặng';
        giftItemLi.insertBefore(badge, giftItemLi.firstChild);

        // Update image
        const img = giftItemLi.querySelector('img');
        if (img) {
          img.src = giftItem.image;
          img.alt = giftItem.name;
        }

        // Update quantity
        const quantity = giftItemLi.querySelector('.order-detail__item-quantity');
        if (quantity) {
          quantity.textContent = giftItem.quantity;
        }

        // Update product info
        const title = giftItemLi.querySelector('h6');
        if (title) {
          title.textContent = giftItem.name;
        }

        const size = giftItemLi.querySelector('.order__item-info__size');
        if (size) {
          size.textContent = giftItem.size;
        }

        const color = giftItemLi.querySelector('.order__item-info__color');
        if (color) {
          color.textContent = 'Đen';
        }

        // Update price
        const priceContainer = giftItemLi.querySelector('.order__item-price');
        if (priceContainer) {
          priceContainer.innerHTML = `
          <span class="gift-price-free">${giftItem.price}${giftItem.currency}</span>
          <span>${giftItem.originalPrice}${giftItem.currency}</span>
        `;
        }
      } else {
        // Fallback to creating new element
        giftItemLi = document.createElement('li');
        giftItemLi.className = 'order-detail__item gift-item';

        giftItemLi.innerHTML = `
        <div class="gift-badge">Tặng</div>
        <div>
          <div class="order-detail__item-thumb">
            <img src="${giftItem.image}" alt="${giftItem.name}">
            <span class="order-detail__item-quantity">${giftItem.quantity}</span>
          </div>
        </div>
        <div class="order-detail__item-info">
          <div class="order__item-info__box">
            <h6>${giftItem.name}</h6>
            <span class="order__item-info__size">${giftItem.size}</span>
            <span class="order__item-info__color">Đen</span>
          </div>
          <div class="order__item-price">
            <span class="gift-price-free">${giftItem.price}${giftItem.currency}</span>
            <span>${giftItem.originalPrice}${giftItem.currency}</span>
          </div>
        </div>
      `;
      }

      // Add gift item to the end of the list
      console.log('Appending gift item to list');
      orderDetailList.appendChild(giftItemLi);
      console.log('Gift item appended, current children count:', orderDetailList.children.length);

      // Store gift item in a way that persists
      giftItemLi.setAttribute('data-gift-persistent', 'true');
      giftItemLi.setAttribute('data-gift-id', giftItem.id);
      giftItemLi.style.cssText = 'position: relative !important; display: flex !important; visibility: visible !important;';

      // Store reference globally to prevent garbage collection
      window.currentGiftItem = giftItemLi;

      // Verify it's actually in the DOM multiple times
      setTimeout(() => {
        const verifyGift = orderDetailList.querySelector('.order-detail__item.gift-item');
        console.log('Gift item verification after 500ms:', verifyGift);
        if (!verifyGift && window.currentGiftItem) {
          console.error('Gift item was removed from DOM! Re-adding...');
          orderDetailList.appendChild(window.currentGiftItem);
        }
      }, 500);

      // Check again after 1 second
      setTimeout(() => {
        const verifyGift = orderDetailList.querySelector('.order-detail__item.gift-item');
        if (!verifyGift && window.currentGiftItem) {
          console.error('Gift item removed again! Force re-adding...');
          orderDetailList.appendChild(window.currentGiftItem);
        }
      }, 1000);

      // Scroll to the newly added gift item and highlight it
      setTimeout(() => {
        // Scroll into view
        giftItemLi.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

        // Add highlight animation
        giftItemLi.style.animation = 'giftHighlight 2s ease-in-out';

        // Remove animation after completion
        setTimeout(() => {
          giftItemLi.style.animation = '';
        }, 2000);
      }, 100);
    });
  };



  const hookIntoCartRendering = () => {
    // Override cart rendering to include gift items
    if (typeof cartUpdater !== 'undefined' && cartUpdater.renderCartItems) {
      const originalRenderCartItems = cartUpdater.renderCartItems;

      cartUpdater.renderCartItems = function (wrapper) {
        // Call original function
        const result = originalRenderCartItems.call(this, wrapper);

        // Add gift items after rendering
        setTimeout(() => {
          const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
          const giftItems = cartItems.filter(item => item.isGift);

          if (giftItems.length > 0) {
            const orderDetailLists = wrapper.querySelectorAll('.order-detail__list');
            orderDetailLists.forEach((orderDetailList, index) => {
              const listType = index === 0 ? 'desktop' : 'mobile';
              giftItems.forEach(giftItem => {
                const existingGift = orderDetailList.querySelector('.order-detail__item.gift-item');
                if (!existingGift) {
                  console.log(`Adding gift item via cart hook to ${listType}:`, giftItem);
                  addGiftItemDirectly(orderDetailList, giftItem);
                }
              });
            });
          }
        }, 50);

        return result;
      };
    }
  };

  const addGiftItemDirectly = (orderDetailList, giftItem) => {
    const giftItemHTML = `
      <li class="order-detail__item gift-item" data-gift-id="${giftItem.product_id}">
        <div class="gift-badge">Tặng</div>
        <div>
          <div class="order-detail__item-thumb">
            <img src="${giftItem.image}" alt="${giftItem.name}">
            <span class="order-detail__item-quantity">${giftItem.quantity}</span>
          </div>
        </div>
        <div class="order-detail__item-info">
          <div class="order__item-info__box">
            <h6>${giftItem.name}</h6>
            <span class="order__item-info__size">${giftItem.selectedSize}</span>
            <span class="order__item-info__color">Đen</span>
          </div>
          <div class="order__item-price">
            <span class="gift-price-free">0đ</span>
            <span>${giftItem.originalPrice}</span>
          </div>
        </div>
      </li>
    `;

    orderDetailList.insertAdjacentHTML('beforeend', giftItemHTML);
  };

  const setupGiftItemObserver = () => {
    const orderDetailLists = document.querySelectorAll('.order-detail__list');

    orderDetailLists.forEach((orderDetailList, index) => {
      const listType = index === 0 ? 'desktop' : 'mobile';

      // Create a MutationObserver to watch for gift item removal
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.removedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE &&
                node.classList &&
                node.classList.contains('gift-item')) {
                console.warn(`Gift item was removed from ${listType} DOM!`, node);
              }
            });
          }
        });
      });

      observer.observe(orderDetailList, {
        childList: true,
        subtree: true
      });
    });
  };

  const init = () => {
    if (!modal) return;

    // Event listeners
    if (openBtn) {
      openBtn.addEventListener('click', openModal);
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', closeModal);
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', closeModal);
    }

    if (confirmBtn) {
      confirmBtn.addEventListener('click', handleConfirm);
      confirmBtn.disabled = true; // Initially disabled
    }

    // Close modal when clicking outside
    modal.addEventListener('click', function (e) {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Handle size selection
    handleSizeSelection();

    // Close modal on Escape key
    document.addEventListener('keydown', function (e) {
      if (e.key === 'Escape' && modal.classList.contains('active')) {
        closeModal();
      }
    });

    // Setup gift item observer
    setupGiftItemObserver();

    // Hook into cart rendering if available
    hookIntoCartRendering();
  };

  return {
    init: init,
    open: openModal,
    close: closeModal
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  checkout.init();
  giftModal.init();
});
