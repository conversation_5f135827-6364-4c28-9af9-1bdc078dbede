package components

import (
    "github.com/networld-solution/gos/templates"
)

templ CheckoutSummaryCpn(){
<div class="checkout__right">
    <div class="checkout__order-detail">
        <ul class="order-detail__list">
            <li class="order-detail__item">
                <div>
                    <div class="order-detail__item-thumb">
                        <img src={templates.AssetURL("/static/images/blazer-black-2.jpg")} alt="Áo blazer form rộng">
                        <span class="order-detail__item-quantity">1</span>
                    </div>
                </div>
                <div class="order-detail__item-info">
                    <div class="order__item-info__box">
                        <h6>Áo blazer form rộng</h6>
                        <span class="order__item-info__size">S</span>
                        <span class="order__item-info__color">Đen</span>
                    </div>
                    <div class="order__item-price">
                        <span>77.000đ</span>
                        <span>469.000đ</span>
                    </div>
                </div>
            </li>
            <li class="order-detail__item">
                <div>
                    <div class="order-detail__item-thumb">
                        <img src={templates.AssetURL("/static/images/blazer-blue.jpg")} alt="Áo blazer form rộng">
                        <span class="order-detail__item-quantity">1</span>
                    </div>
                </div>
                <div class="order-detail__item-info">
                    <div class="order__item-info__box">
                        <h6>Áo blazer form rộng</h6>
                        <span class="order__item-info__size">L</span>
                        <span class="order__item-info__color">Xanh dương</span>
                    </div>
                    <div class="order__item-price">
                        <span>77.000đ</span>
                        <span>469.000đ</span>
                    </div>
                </div>
            </li>
        </ul>

        <div class="product-detail__gift">
            <div class="gift-product">
                <div class="gift-product__badge">
                    <svg class="gift-product__icon" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V7H1V9H3V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V9H21ZM19 19H5V9H19V19Z" />
                    </svg>
                    Ưu đãi dành riêng cho bạn
                </div>
                <div class="gift-product__content" data-gift-id="gift-quan-short-clean-cut"
                    data-gift-name="Quần Short Clean Cut" data-gift-image="/static/images/quan-maybi.jpg"
                    data-gift-price="0" data-gift-original-price="120000" data-gift-currency="đ"
                    data-gift-sizes='["S","M","L","XL"]'
                    data-gift-colors='[{"color":"#000000","img":"/static/images/quan-maybi.jpg"}]'
                    data-gift-sku="GIFT-QSC-001" data-gift-condition="Mua đơn từ 500k">
                    <div class="gift-product__image">
                        <img src={templates.AssetURL("/static/images/quan-maybi.jpg")} alt="Quần Short" />
                    </div>
                    <div class="gift-product__info">
                        <div class="gift-product__details">
                            <h3 class="gift-product__title">Quần Short Clean Cut</h3>
                            <p class="gift-product__desc">Tặng khi mua đơn Áo blazer form rộng</p>
                            <div class="gift-product__price">
                                <span class="gift-product__price-current">0đ</span>
                                <span class="gift-product__price-original">120.000đ</span>
                            </div>
                        </div>
                        <div class="gift-product__actions">
                            <button class="gift-product__size-btn btn btn--outline" type="button"
                                id="openGiftSizeModal">
                                <span>Lấy ngay</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="order__shipping-method">
            <h2>Phương thức vận chuyển</h2>
            <div class="shipping-method__wrapper">
                <div class="shipping-method__checkbox">
                    <input class="form-check-input radio" type="radio" id="flexRadioDefault5" name="shipping_method"
                        checked>
                    <label class="form-check-label" for="flexRadioDefault5">Giao hàng tận nơi - Thu tiền sau
                        (COD)</label>
                </div>
                <span>30.000đ</span>
            </div>
        </div>
        <div class="order-detail__summary">
            <div class="order__summary-discount">
                <div class="summary-discount__box-content">
                    <div class="summary-discount__content">
                        <input placeholder=" " class="discount-input" size="30" type="text" id="discount_code"
                            name="discount_code" value="">
                        <label class="discount-label" for="discount_code">Mã giảm giá</label>
                    </div>
                    <button type="submit" class="summary-discount__btn-submit" disabled>
                        <span class="btn-content">Sử dụng</span>
                    </button>
                </div>
                <div class="discount__choose-coupons btn-open-vouchers">
                    <div class="discount__choose-coupons__content">
                        <svg>
                            <use href="#icon-voucher"></use>
                        </svg>
                        <span>Xem thêm mã giảm giá</span>
                    </div>
                    <div class="cart-vouchers__selected">
                        <div class="cart-vouchers__item freeship">
                            <span class="cart-vouchers__chip freeship">
                                <span>Giảm 18%</span>
                            </span>
                        </div>
                        <div class="cart-vouchers__item discount">
                            <span class="cart-vouchers__chip discount">
                                <span>Giảm 10%</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="order__summary-subtotal">
                <span>Tạm tính</span>
                <span data-type="subtotal">184.000đ</span>
            </div>
            <div class="order__summary-shipping">
                <span>Phí vận chuyển</span>
                <span>30.000đ</span>
            </div>
            <div class="order__summary-discount-price">
                <span>Mã giảm giá</span>
                <span data-type="discount">-30.000đ</span>
            </div>
            <div class="order__summary-total">
                <span>Tổng cộng</span>
                <span data-type="total">184.000đ</span>
            </div>
        </div>

        <div class="checkout__privacy-note">
            <p class="text">
                Your personal data will be used to process your order, support your experience throughout this website,
                and for other purposes described in our
                <a href="#">privacy policy.</a>
            </p>
        </div>
        <div class="checkout__form-group">
            <div class="form__checkbox-wrapper">
                <input type="checkbox" class="form__checkbox" id="checkboxTerms" checked>
                <label class="form__checkbox-label" for="checkboxTerms">
                    Tôi đã đọc và đồng ý với các điều khoản và điều kiện của Maybi.
                </label>
            </div>
        </div>
        <a href="#" class="btn btn--primary"><span>Thanh toán</span></a>
    </div>
</div>

<!-- Gift Size Modal -->
<div class="gift-modal-overlay" id="giftSizeModal">
    <div class="gift-modal">
        <div class="gift-modal__header">
            <h5 class="gift-modal__title">Chọn size cho sản phẩm tặng</h5>
            <button type="button" class="gift-modal__close" id="closeGiftSizeModal" aria-label="Close">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </button>
        </div>
        <div class="gift-modal__body">
            <div class="gift-size-selector">
                <div class="gift-size-product" id="giftSizeProductInfo">
                    <img id="giftModalImage" src={templates.AssetURL("/static/images/quan-maybi.jpg")}
                        alt="Quần Short Clean Cut" />
                    <div class="gift-size-info">
                        <h6 id="giftModalTitle">Quần Short Clean Cut</h6>
                        <p id="giftModalDescription">Mua đơn từ 500k</p>
                    </div>
                </div>
                <div class="gift-size-options">
                    <h6>Chọn size:</h6>
                    <div class="gift-size-list">
                        <div class="product-size__item" data-size="S">S</div>
                        <div class="product-size__item" data-size="M">M</div>
                        <div class="product-size__item" data-size="L">L</div>
                        <div class="product-size__item" data-size="XL">XL</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="gift-modal__footer">
            <button type="button" class="btn btn--outline" id="cancelGiftSize">Hủy</button>
            <button type="button" class="btn btn--primary" id="confirmGiftSize">Xác nhận</button>
        </div>
    </div>
</div>
}