package components

import (
    "github.com/networld-solution/gos/templates"
)

templ ContentProductDetailCpn(){
    <div class="product-detail__content">
        <div class="product-detail__content-top">
            <div class="product-detail__content-box">
                <span class="product-detail__label product-detail__sale-percent">-20%</span>
                <span class="product-detail__label product-detail__sale-hot">hot</span>
                <span class="product-detail__label">M<PERSON><PERSON> về</span>
            </div>
            <h1 class="product-detail__title">
                Áo blazer form rộng
            </h1>
            <div class="product-detail__metadata comma-separated">
                <div class="product-detail__trademark">
                    <p><strong>Thương <PERSON>:</strong> Maybi</p>
                    <p><strong>M<PERSON> sản phẩm:</strong> PRT584E63A</p>
                </div>
                // <p>
                //     <strong>Danh mục:</strong>
                //     <a class="comma-item" href="#" title="Đầm"><PERSON><PERSON><PERSON></a>
                //     <a class="comma-item" href="#" title="Jean<PERSON>">Jean<PERSON></a>
                //     <a class="comma-item" href="#" title="Công sở">Công sở</a>
                // </p>
                // <p><strong>Nhãn:</strong>
                //     <a class="comma-item" href="#" title="Thường ngày">Thường ngày</a>
                //     <a class="comma-item" href="#" title="Thể thao">Thể thao</a>
                //     <a class="comma-item" href="#" title="Văn phòng">Văn phòng</a>
                // </p>
            </div>
            <div class="product-rating">
                <div class="mb-rating">
                    <svg class="mb-start-fill">
                        <use href="#icon-star-active"></use>
                    </svg>
                    <svg class="mb-start-fill">
                        <use href="#icon-star-active"></use>
                    </svg>
                    <svg class="mb-start-fill">
                        <use href="#icon-star-active"></use>
                    </svg>
                    <svg class="mb-start-fill">
                        <use href="#icon-star-active"></use>
                    </svg>
                    <svg class="mb-start">
                        <use href="#icon-star-fill"></use>
                    </svg>
                </div>
                <div class="product-rating__selled">Đã bán 200+</div>
            </div>
            
            <div class="product-detail__promotion" id="ega-salebox">
                <h3 class="product-promotion__heading">
                    <svg class="product-promotion__icon">
                        <use href="#icon-gift-box"></use>
                    </svg>
                    ƯU ĐÃI THÁNG 07
                </h3>
                <ul>
                    <li>GIẢM GIÁ BST NÀNG - kênh có giá tốt nhất</li>
                    <li>Mua nhiều nhận voucher giảm tiền 20.000 - 90.000VNĐ /đơn</li>
                    <li>Freeship toàn quốc cho đơn hàng từ 249K</li>
                </ul>
            </div>

            <div class="another-deals">
                <div class="card">
                    <div class="icon-sales">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M10.0544 2.0941C11.1756 1.13856 12.8248 1.13855 13.9461 2.09411L15.2941 3.24286C15.4542 3.37935 15.6533 3.46182 15.8631 3.47856L17.6286 3.61945C19.0971 3.73663 20.2633 4.9028 20.3805 6.37131L20.5214 8.13679C20.5381 8.34654 20.6205 8.54568 20.757 8.70585L21.9058 10.0539C22.8614 11.1751 22.8614 12.8243 21.9058 13.9456L20.757 15.2935C20.6206 15.4537 20.538 15.6529 20.5213 15.8627L20.3805 17.6281C20.2633 19.0967 19.0971 20.2628 17.6286 20.3799L15.8631 20.5208C15.6533 20.5376 15.4542 20.6201 15.2941 20.7566L13.9461 21.9053C12.8248 22.8609 11.1756 22.8608 10.0543 21.9053L8.70631 20.7566C8.54615 20.6201 8.34705 20.5376 8.1373 20.5209L6.37184 20.3799C4.9033 20.2627 3.73716 19.0966 3.61997 17.6281L3.47906 15.8627C3.46232 15.6529 3.37983 15.4538 3.24336 15.2936L2.0946 13.9455C1.13905 12.8243 1.13904 11.1752 2.09458 10.0539L3.24334 8.70589C3.37983 8.54573 3.46234 8.34654 3.47907 8.13678L3.61996 6.3713C3.73714 4.90278 4.90327 3.73665 6.3718 3.61946L8.13729 3.47857C8.34705 3.46183 8.54619 3.37935 8.70636 3.24286L10.0544 2.0941ZM12.6488 3.61632C12.2751 3.29782 11.7253 3.29781 11.3516 3.61632L10.0036 4.76509C9.5231 5.17456 8.92568 5.42201 8.29637 5.47223L6.5309 5.61312C6.04139 5.65219 5.65268 6.04089 5.61362 6.53041L5.47272 8.29593C5.4225 8.92521 5.17505 9.52259 4.76559 10.0031L3.61683 11.3511C3.29832 11.7248 3.29831 12.2746 3.61683 12.6483L4.76559 13.9963C5.17506 14.4768 5.4225 15.0743 5.47275 15.7035L5.61363 17.469C5.65268 17.9585 6.04139 18.3473 6.53092 18.3863L8.29636 18.5272C8.92563 18.5774 9.5231 18.8249 10.0036 19.2344L11.3516 20.3831C11.7254 20.7016 12.2751 20.7016 12.6488 20.3831L13.9969 19.2343C14.4773 18.8249 15.0747 18.5774 15.704 18.5272L17.4695 18.3863C17.959 18.3472 18.3478 17.9585 18.3868 17.469L18.5277 15.7035C18.5779 15.0742 18.8253 14.4768 19.2349 13.9964L20.3836 12.6483C20.7022 12.2746 20.7021 11.7249 20.3836 11.3511L19.2348 10.0031C18.8253 9.52259 18.5779 8.92519 18.5277 8.2959L18.3868 6.53041C18.3478 6.0409 17.959 5.65219 17.4695 5.61312L15.704 5.47224C15.0748 5.42203 14.4773 5.17455 13.9968 4.76508L12.6488 3.61632ZM14.8284 7.75718L16.2426 9.1714L9.17154 16.2425L7.75733 14.8282L14.8284 7.75718ZM10.2322 10.232C9.64641 10.8178 8.69667 10.8178 8.11088 10.232C7.52509 9.6463 7.52509 8.69652 8.11088 8.11073C8.69667 7.52494 9.64641 7.52494 10.2322 8.11073C10.818 8.69652 10.818 9.6463 10.2322 10.232ZM13.7677 15.8889C14.3535 16.4747 15.3032 16.4747 15.889 15.8889C16.4748 15.3031 16.4748 14.3534 15.889 13.7676C15.3032 13.1818 14.3535 13.1818 13.7677 13.7676C13.1819 14.3534 13.1819 15.3031 13.7677 15.8889Z"></path></svg>
                    </div>
                    <div class="loader">
                        <p>Tổng hợp ưu đãi trong tháng:</p>
                        <div class="words">
                            <span class="word">Ðồng giá 399k</span>
                            <span class="word">Mua 3 tặng 1</span>
                            <span class="word">Ðồng giá 77k</span>
                            <span class="word">Đồ ngủ 139k</span>
                            <span class="word">Mua 2 giá giảm 30%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="product-detail__meta">
                <div class="product-detail__meta-right">
                    <div class="product-detail__price">
                        <span>419.000đ</span>
                        <span>599.000đ</span>
                    </div>
                </div>
            </div>
            <div class="product-detail__options product__info">
                <div class="product-detail__options-left">
                    <label class="product-detail__label-text">Kích thước</label>
                    <ul class="product-size">
                        <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}]'
                            class="product-size__item maybi-hover">S
                        </li>
                        <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}]'
                            class="product-size__item maybi-hover">M
                        </li>
                        <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}, {"color": "#eb6609", "img": "/static/images/blazer-cam.jpg"}]'
                            class="product-size__item maybi-hover product-size--active">L
                        </li>
                    </ul>
                </div>
                <div class="product-detail__options-center">
                    <label class="product-detail__label-text">Số lượng</label>
                    <div class="product-detail__quantity-control quantity-control">
                        <div class="product-detail__quantity-inline">
                            <button class="product-detail__btn quantity__btn-down" type="button" aria-label="Giảm số lượng">
                                <svg class="product-icon__down">
                                    <use href="#icon-down"></use>
                                </svg>
                            </button>
                            <input class="product-detail__quantity-input quantity-input" type="text" value="1" aria-label="Số lượng sản phẩm"/>
                            <button class="product-detail__btn quantity__btn-up" type="button" aria-label="Tăng số lượng">
                                <svg class="product-icon__up">
                                    <use href="#icon-up"></use>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="product-detail__options-right">
                    <label class="product-detail__label-text">Màu</label>
                    <ul class="product-colors">
                        <li class="product-color active" data-img={templates.AssetURL("/static/images/blazer-den.jpg")}>
                            <span class="product-color__item" style="background-color: black;"></span>
                        </li>
                        <li class="product-color" data-img={templates.AssetURL("/static/images/blazer-xanh-reu.jpg")}>
                            <span class="product-color__item" style="background-color: #607c2c;"></span>
                        </li>
                        <li class="product-color" data-img={templates.AssetURL("/static/images/blazer-green.jpg")}>
                            <span class="product-color__item" style="background-color: #225c48;"></span>
                        </li>
                        <li class="product-color" data-img={templates.AssetURL("/static/images/blazer-blue.jpg")}>
                            <span class="product-color__item" style="background-color: #c9e6ff;"></span>
                        </li>
                        <li class="product-color" data-img={templates.AssetURL("/static/images/blazer-pink.jpg")}>
                            <span class="product-color__item" style="background-color: #f32250;"></span>
                        </li>
                        <li class="product-color" data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                            <span class="product-color__item" style="background-color: #eb6609;"></span>
                        </li>
                        <li class="product-color" data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                            <span class="product-color__item" style="background-color: #6e7ec3;"></span>
                        </li>
                        <li class="product-color" data-img={templates.AssetURL("/static/images/blazer-cam.jpg")}>
                            <span class="product-color__item" style="background-color: #f7b789;"></span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="product-detail__support">
                <div class="product-detail__return-policy">
                    <svg class="">
                        <use href="#icon-return-policy"></use>
                    </svg>
                    <span>Chính sách đổi hàng</span>
                </div>
                <div class="product-detail__size-guide">
                    <svg class="">
                        <use href="#icon-ruler"></use>
                    </svg>
                    <span>Hướng dẫn chọn size</span>
                </div>
            </div>
            <div class="product-detail__actions">
                <a href="#" class="btn btn--primary">
                    <span>Thêm vào giỏ</span>
                </a>
                <a href={templates.AssetURL("/thanh-toan")} class="btn btn--outline">
                    <span>Mua ngay</span>
                </a>
                <div class="bookmark-btn style-1"><input class="form-check-input" id="favoriteCheck1"
                        type="checkbox">
                    <label class="form-check-label" for="favoriteCheck1">
                        <svg class="product-icon__heart">
                            <use href="#icon-heart"></use>
                        </svg>
                    </label>
                </div>
            </div>

            <div class="product-detail__gift">
                <div class="gift-product">
                    <div class="gift-product__badge">
                        <svg class="gift-product__icon" xmlns="http://www.w3.org/2000/svg" width="19" height="17" viewBox="0 0 19 17" fill="none"><path d="M4.20845 5.33386C4.09683 5.33386 3.98979 5.3782 3.91086 5.45713C3.83194 5.53605 3.7876 5.6431 3.7876 5.75472V6.66876C3.7876 6.78037 3.83194 6.88742 3.91086 6.96635C3.98979 7.04527 4.09683 7.08961 4.20845 7.08961C4.32007 7.08961 4.42711 7.04527 4.50604 6.96635C4.58497 6.88742 4.6293 6.78037 4.6293 6.66876V5.75472C4.6293 5.6431 4.58497 5.53605 4.50604 5.45713C4.42711 5.3782 4.32007 5.33386 4.20845 5.33386Z" fill="white"/> <path d="M4.20845 7.61978C4.09683 7.61978 3.98979 7.66412 3.91086 7.74305C3.83194 7.82197 3.7876 7.92902 3.7876 8.04064V8.95468C3.7876 9.06629 3.83194 9.17334 3.91086 9.25227C3.98979 9.33119 4.09683 9.37553 4.20845 9.37553C4.32007 9.37553 4.42711 9.33119 4.50604 9.25227C4.58497 9.17334 4.6293 9.06629 4.6293 8.95468V8.04064C4.6293 7.92902 4.58497 7.82197 4.50604 7.74305C4.42711 7.66412 4.32007 7.61978 4.20845 7.61978Z" fill="white"/> <path d="M4.20845 9.90572C4.09683 9.90572 3.98979 9.95006 3.91086 10.029C3.83194 10.1079 3.7876 10.215 3.7876 10.3266V11.2406C3.7876 11.3522 3.83194 11.4593 3.91086 11.5382C3.98979 11.6171 4.09683 11.6615 4.20845 11.6615C4.32007 11.6615 4.42711 11.6171 4.50604 11.5382C4.58497 11.4593 4.6293 11.3522 4.6293 11.2406V10.3266C4.6293 10.215 4.58497 10.1079 4.50604 10.029C4.42711 9.95006 4.32007 9.90572 4.20845 9.90572Z" fill="white"/> <path d="M2.89008 0.50473C2.39989 0.545773 1.95331 0.872474 1.79027 1.36534L1.22968 3.0578C0.546192 3.11496 0 3.68892 0 4.38612V6.06049C0.000134673 6.2431 0.117999 6.40479 0.291803 6.4608C0.816822 6.63024 1.26238 7.28984 1.26256 8.09736C1.26263 8.90513 0.816991 9.56524 0.291803 9.73474C0.117999 9.79075 0.000134673 9.95244 0 10.135V12.6503C0 13.3848 0.604476 13.9893 1.339 13.9893H7.98307L15.1285 16.4289C15.7882 16.6542 16.5099 16.2855 16.7273 15.6283L17.2731 13.9794C17.9635 13.9295 18.5176 13.3525 18.5176 12.6503V10.1342C18.5174 9.95162 18.3996 9.78993 18.2258 9.73392C17.7007 9.56447 17.2552 8.90487 17.255 8.09736C17.2549 7.28958 17.7006 6.62948 18.2258 6.45997C18.3996 6.40397 18.5174 6.24228 18.5176 6.05967V4.38612C18.5176 3.65159 17.9131 3.04711 17.1786 3.04711H10.5361L3.39149 0.564735C3.22608 0.506811 3.0458 0.514133 2.89008 0.50473ZM2.95173 1.33986C3.0063 1.33614 3.06324 1.34332 3.12023 1.36205C4.73684 1.92342 6.35336 2.48535 7.96992 3.04711H2.11989L2.58923 1.6292C2.6458 1.45821 2.788 1.35103 2.95173 1.33986ZM1.339 3.88882H3.78768V4.28913C3.78768 4.52156 3.97611 4.70998 4.20854 4.70998C4.44097 4.70998 4.62939 4.52156 4.62939 4.28913V3.88882H17.1786C17.4614 3.88882 17.6759 4.10325 17.6759 4.38612V5.81883C16.8986 6.22833 16.4132 7.11084 16.4133 8.09736C16.4135 9.08356 16.8988 9.96573 17.6759 10.3751V12.6503C17.6759 12.9332 17.4614 13.1476 17.1786 13.1476H4.62857C4.63066 13.0023 4.62939 12.8529 4.62939 12.7062C4.62939 12.4738 4.44097 12.2853 4.20854 12.2853C3.97611 12.2853 3.78768 12.4738 3.78768 12.7062C3.78684 12.8523 3.78804 13.0033 3.78852 13.1476H1.33903C1.05608 13.1476 0.841707 12.9332 0.841707 12.6503V10.3759C1.61897 9.96639 2.10436 9.08388 2.10427 8.09736C2.10405 7.11115 1.61875 6.22899 0.841707 5.81965V4.38612C0.841707 4.10325 1.05614 3.88882 1.339 3.88882ZM10.5879 13.9893H16.3829L15.9283 15.3637C15.8528 15.5919 15.6235 15.7086 15.4006 15.6324L10.5879 13.9893Z" fill="white"/> <path d="M12.9465 9.15636C13.6956 9.15636 14.3118 9.77249 14.3118 10.5217C14.3118 11.2708 13.6956 11.8878 12.9465 11.8878C12.1973 11.8878 11.5803 11.2708 11.5803 10.5217C11.5803 9.77249 12.1973 9.15636 12.9465 9.15636ZM12.9465 9.99806C12.6522 9.99806 12.422 10.2274 12.422 10.5217C12.422 10.8159 12.6522 11.0461 12.9465 11.0461C13.2407 11.0461 13.4701 10.8159 13.4701 10.5217C13.4701 10.2274 13.2407 9.99806 12.9465 9.99806Z" fill="white"/> <path d="M8.93864 5.14885C9.68781 5.14885 10.3039 5.76498 10.3039 6.51416C10.3039 7.26333 9.68781 7.87947 8.93864 7.87947C8.18946 7.87947 7.57251 7.26333 7.57251 6.51416C7.57251 5.76498 8.18947 5.14885 8.93864 5.14885ZM8.93864 5.99056C8.64436 5.99056 8.41422 6.21988 8.41422 6.51416C8.41422 6.80844 8.64436 7.03776 8.93864 7.03776C9.23292 7.03776 9.46224 6.80844 9.46224 6.51416C9.46224 6.21988 9.23292 5.99056 8.93864 5.99056Z" fill="white"/> <path d="M12.6485 6.21667L8.64057 10.2246C8.47626 10.389 8.47626 10.6554 8.64057 10.8198C8.80491 10.9841 9.07133 10.9841 9.23568 10.8198L13.2437 6.81178C13.408 6.64743 13.408 6.38101 13.2437 6.21667C13.0717 6.06382 12.8106 6.0478 12.6485 6.21667Z" fill="white"/></svg>
                        Ưu đãi dành riêng cho bạn
                    </div>
                    <div class="gift-product__content"
                         data-gift-id="gift-quan-short-clean-cut"
                         data-gift-name="Quần Short Clean Cut"
                         data-gift-image="/static/images/quan-maybi.jpg"
                         data-gift-price="0"
                         data-gift-original-price="120000"
                         data-gift-currency="đ"
                         data-gift-sizes='["S","M","L","XL"]'
                         data-gift-colors='[{"color":"#000000","img":"/static/images/quan-maybi.jpg"}]'
                         data-gift-sku="GIFT-QSC-001"
                         data-gift-condition="Mua đơn từ 500k">
                        <div class="gift-product__image">
                            <img src={templates.AssetURL("/static/images/quan-maybi.jpg")} alt="Quần Short" />
                        </div>
                        <div class="gift-product__info">
                            <div class="gift-product__details">
                                <h3 class="gift-product__title">Quần Short Clean Cut</h3>
                                <p class="gift-product__desc">Tặng khi mua đơn Áo blazer form rộng</p>
                                <div class="gift-product__price">
                                    <span class="gift-product__price-current">0đ</span>
                                    <span class="gift-product__price-original">120.000đ</span>
                                </div>
                            </div>
                            <div class="gift-product__actions">
                                <button class="gift-product__size-btn btn btn--outline" type="button" id="openGiftSizeModal">
                                    <span>Lấy ngay</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="product-detail__content-center">
            <div class="product-addon-deals">
                <h3 class="addon-deals__title">Ưu đãi khi mua kèm</h3>
                <div class="addon-deals__content">
                    <div class="addon-deals__content-wrapper product">
                        <a class="addon-deals__thumb" href="#">
                            <img class="product__img" src={templates.AssetURL("/static/images/dam-om-thun.webp")}
                                alt="Sale Up to 50% Off" />
                        </a>
                        <div class="addon-deals__info">
                            <div class="addon-deals__info-top">
                                <div class="addon-deals__top-left">
                                    <div class="addon-deals__top-title">
                                        <a href="#" title="Đầm xòe chéo nhung hàn tùng rã nhún dáng midi">Đầm thun ôm
                                            dáng midi, lệch vai, nhún hông, rút dây</a>
                                    </div>
                                    // <div class="addon-deals__top-tags comma-separated">
                                    //     <p>
                                    //         <a class="comma-item" href="#">Đầm</a>
                                    //         <a class="comma-item" href="#">Váy</a>
                                    //     </p>
                                    // </div>
                                </div>
                                <div class="addon-deals__top-right">
                                    <div class="product-rating">
                                        <div class="mb-rating"><svg class="mb-start-fill">
                                                <use href="#icon-star-active"></use>
                                            </svg> <svg class="mb-start-fill">
                                                <use href="#icon-star-active"></use>
                                            </svg> <svg class="mb-start-fill">
                                                <use href="#icon-star-active"></use>
                                            </svg> <svg class="mb-start-fill">
                                                <use href="#icon-star-active"></use>
                                            </svg> <svg class="mb-start">
                                                <use href="#icon-star-fill"></use>
                                            </svg></div>
                                        <div class="product-rating__selled">Đã bán 200+</div>
                                    </div>
                                </div>
                            </div>
                            <div class="addon-deals__info-bot">
                                <div class="addon-deals__bot-left product__info">
                                    <div class="addon-deals__price">
                                        <label class="product-detail__label-text">Giá</label>
                                        <div class="product-detail__price">
                                            <span>419.000đ</span>
                                            <span>599.000đ</span>
                                        </div>
                                    </div>
                                    <div class="addon-deals__size">
                                        <label class="product-detail__label-text">Kích thước</label>
                                        <ul class="product-size">
                                            <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}]'
                                                class="product-size__item maybi-hover">S
                                            </li>
                                            <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}]'
                                                class="product-size__item maybi-hover">M
                                            </li>
                                            <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}, {"color": "#eb6609", "img": "/static/images/blazer-cam.jpg"}]'
                                                class="product-size__item maybi-hover product-size--active">L
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="addon-deals__color">
                                        <label>Màu</label>
                                        <ul class="product-colors">
                                            <li class="product-color active" data-img="/static/images/blazer-den.jpg">
                                                <span class="product-color__item"
                                                    style="background-color: black;"></span>
                                            </li>
                                            <li class="product-color" data-img="/static/images/blazer-xanh-reu.jpg">
                                                <span class="product-color__item"
                                                    style="background-color: #607c2c;"></span>
                                            </li>
                                            <li class="product-color" data-img="/static/images/blazer-green.jpg">
                                                <span class="product-color__item"
                                                    style="background-color: #225c48;"></span>
                                            </li>
                                            <li class="product-color" data-img="/static/images/blazer-blue.jpg">
                                                <span class="product-color__item"
                                                    style="background-color: #c9e6ff;"></span>
                                            </li>
                                            <li class="product-color" data-img="/static/images/blazer-pink.jpg">
                                                <span class="product-color__item"
                                                    style="background-color: #f32250;"></span>
                                            </li>
                                            <li class="product-color" data-img="/static/images/blazer-cam.jpg">
                                                <span class="product-color__item"
                                                    style="background-color: #eb6609;"></span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="addon-deals__bot-right">
                                    <a href="#" class="btn btn--primary"><span>Thêm vào giỏ</span></a>
                                    <a href={templates.AssetURL("/thanh-toan")} class="btn btn--outline">
                                        <span>Mua ngay</span>
                                    </a>
                                    <div class="bookmark-btn style-1"><input class="form-check-input" id="favoriteCheck3" type="checkbox">
                                        <label class="form-check-label" for="favoriteCheck3">
                                            <svg class="product-icon__heart">
                                                <use href="#icon-heart"></use>
                                            </svg>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="product-detail__content-bot">
            <div class="product-detail__social-icon">
                <p><strong>Chia sẻ:</strong></p>
                <ul>
                    <li>
                        <a target="_blank" class="text-dark" href="#" aria-label="Facebook">
                            <svg class="product-icon__social-item">
                                <use href="#icon-facebook"></use>
                            </svg>
                        </a>
                    </li>
                    <li>
                        <a target="_blank" class="text-dark" href="#" aria-label="Instagram">
                            <svg class="product-icon__social-item">
                                <use href="#icon-instagram"></use>
                            </svg>
                        </a>
                    </li>
                    <li>
                        <a target="_blank" class="text-dark" href="#" aria-label="Youtube">
                            <svg class="product-icon__social-item">
                                <use href="#icon-youtube"></use>
                            </svg>
                        </a>
                    </li>
                </ul>
            </div>
            <ul class="product-detail__order-benefits">
                <li class="order-benefits__item">
                    <div class="order-benefits__item-icon">
                        <svg>
                            <use href="#icon-shipping"></use>
                        </svg>
                    </div>
                    <div class="order-benefits__item-info">
                        <span>MIỄN PHÍ</span>
                        <h6><strong>Vận chuyển</strong></h6>
                    </div>
                </li>
                <li class="order-benefits__item">
                    <div class="order-benefits__item-icon">
                        <svg>
                            <use href="#icon-fast-delivery"></use>
                        </svg>
                    </div>
                    <div class="order-benefits__item-info">
                        <span>Đổi trả dễ dàng</span>
                        <h6><strong>30 ngày</strong></h6>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <!-- Gift Size Modal -->
    <div class="gift-modal-overlay" id="giftSizeModal">
        <div class="gift-modal">
            <div class="gift-modal__header">
                <h5 class="gift-modal__title">Chọn size cho sản phẩm tặng</h5>
                <button type="button" class="gift-modal__close" id="closeGiftSizeModal" aria-label="Close">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <div class="gift-modal__body">
                <div class="gift-size-selector">
                    <div class="gift-size-product" id="giftSizeProductInfo">
                        <img id="giftModalImage" src={templates.AssetURL("/static/images/quan-maybi.jpg")} alt="Quần Short Clean Cut" />
                        <div class="gift-size-info">
                            <h6 id="giftModalTitle">Quần Short Clean Cut</h6>
                            <p id="giftModalDescription">Tặng khi mua đơn Áo blazer form rộng</p>
                        </div>
                    </div>
                    <div class="gift-size-options">
                        <label class="gift-size-label">Kích thước</label>
                        <ul class="product-size gift-size-list">
                            <li class="product-size__item product-size--active maybi-hover" data-size="S">S</li>
                            <li class="product-size__item maybi-hover" data-size="M">M</li>
                            <li class="product-size__item maybi-hover" data-size="L">L</li>
                            <li class="product-size__item maybi-hover" data-size="XL">XL</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="gift-modal__footer">
                <button type="button" class="btn btn--outline" id="cancelGiftSize">
                    <span>Hủy</span>
                </button>
                <button type="button" class="btn btn--primary" id="confirmGiftSize">
                    <span>Xác nhận</span>
                </button>
            </div>
        </div>
    </div>
}