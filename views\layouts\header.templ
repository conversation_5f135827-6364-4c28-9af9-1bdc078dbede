package layouts

import (
    "goweb/views/layouts/header"
    "github.com/networld-solution/gos/templates"
)

templ Header() {
<header class="header">
    @header.HeaderTop()
    <div class="header-wrapper">
        <div class="container90">
            <div class="header-navbars">
                @logoMenuSticky()
                @header.CategoryMenu()
                @header.NavMenu()
                @header.ActionIcons()
                @menuMobileBar()
                @header.MenusMobile()
            </div>
        </div>
        @header.SearchDropdown()
    </div>    
</header>
}

templ logoMenuSticky() {
    <a class="maybi__logo" href={templates.AssetURL("/")} title="Maybi">                       
        <img class="maybi__logo__img" width="140" height="39" src={templates.AssetURL("/static/images/logo.webp")} alt="Maybi Ecommerce Logo"> 
    </a>
}

templ menuMobileBar() {
    <button class="menu-mobile-bar" type="button">
        <span></span>
        <span></span>
        <span></span>
    </button>
}